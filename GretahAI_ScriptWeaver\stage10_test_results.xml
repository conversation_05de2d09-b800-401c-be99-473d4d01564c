<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="0" tests="1" time="23.209" timestamp="2025-05-31T01:10:30.836913" hostname="sbezugam"><testcase classname="test_stage10_execution" name="test_simple_navigation" time="18.582"><properties><property name="artifact_screenshot" value="C:\GenAIJira\JiraOllamaPython\GRETAH-CaseForge\GretahAI_ScriptWeaver\screenshots\test_simple_navigation_20250531_011045.png" /><property name="artifact_page_source" value="C:\GenAIJira\JiraOllamaPython\GRETAH-CaseForge\GretahAI_ScriptWeaver\page_sources\test_simple_navigation_20250531_011045.html" /><property name="current_url" value="https://httpbin.org/" /><property name="url_capture_timestamp" value="20250531_011045" /><property name="performance_metrics" value="{'execution_time': 9.28816819190979, 'memory_usage': -0.35546875, 'cpu_usage': 46.0, 'network_requests': 0, 'network_bytes': 0}" /><property name="artifact_screenshot" value="C:\GenAIJira\JiraOllamaPython\GRETAH-CaseForge\GretahAI_ScriptWeaver\screenshots\test_simple_navigation_20250531_011045.png" /><property name="artifact_page_source" value="C:\GenAIJira\JiraOllamaPython\GRETAH-CaseForge\GretahAI_ScriptWeaver\page_sources\test_simple_navigation_20250531_011045.html" /><property name="current_url" value="https://httpbin.org/" /><property name="url_capture_timestamp" value="20250531_011045" /><property name="perf_execution_time" value="9.28816819190979" /><property name="perf_memory_usage" value="-0.35546875" /><property name="perf_cpu_usage" value="46.0" /><property name="perf_network_requests" value="0" /><property name="perf_network_bytes" value="0" /></properties></testcase></testsuite></testsuites>