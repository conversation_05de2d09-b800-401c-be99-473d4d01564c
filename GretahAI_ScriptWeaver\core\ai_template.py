"""
AI Template Module for GretahAI ScriptWeaver Stage 10.

This module provides specialized AI functionality for template-based script generation
in the Script Playground (Stage 10). It follows the established modular AI architecture
used throughout the application.

Functions:
    generate_template_based_script: Main function for template-based script generation
    extract_python_code_from_response: Helper function to clean AI responses
    _execute_template_ai_call: Internal function for AI execution with comprehensive logging
    _log_template_generation_process: Internal function for detailed process logging
"""

import logging
import time
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime

# Import the centralized AI function and logging utilities
from .ai import generate_llm_response, log_ai_interaction
from .template_helpers import extract_template_structure_info
from .ai_helpers import error_handler
from debug_utils import debug

# Set up logging
logger = logging.getLogger("ScriptWeaver.core.ai_template")


# ═══════════════════════════════════════════════════════════════════════════════
# PROMPT GENERATION FUNCTIONS (Moved from template_prompt_builder.py)
# ═══════════════════════════════════════════════════════════════════════════════

def _format_target_steps(steps: List[Dict[str, Any]]) -> str:
    """
    Format target test case steps for inclusion in the prompt.

    Args:
        steps: List of test case steps

    Returns:
        Formatted string representation of steps
    """
    try:
        if not steps:
            return "- No steps defined"

        formatted_steps = []
        for i, step in enumerate(steps, 1):
            action = step.get('action', 'No action specified')
            expected = step.get('expected_result', 'No expected result specified')

            # Truncate long descriptions
            if len(action) > 100:
                action = action[:97] + "..."
            if len(expected) > 100:
                expected = expected[:97] + "..."

            formatted_steps.append(f"  {i}. **Action**: {action}")
            formatted_steps.append(f"     **Expected**: {expected}")

        return "\n".join(formatted_steps)

    except Exception as e:
        logger.error(f"Failed to format target steps: {e}")
        return "- Error formatting steps"


def _generate_fallback_prompt(target_test_case: Dict[str, Any]) -> str:
    """
    Generate a fallback prompt when template-based generation fails.

    Args:
        target_test_case: The target test case

    Returns:
        Basic prompt for script generation
    """
    try:
        target_tc_id = target_test_case.get('Test Case ID', 'Unknown')
        target_objective = target_test_case.get('Test Case Objective', 'No objective specified')

        return f"""# Fallback Test Script Generation

## Task
Generate a PyTest automation script for the following test case.

## Test Case Information
- **Test Case ID**: {target_tc_id}
- **Objective**: {target_objective}

## Instructions
Create a basic PyTest script that implements the test case requirements.
Include proper imports, browser setup, and error handling.

Generate the Python script code now.
"""

    except Exception as e:
        logger.error(f"Failed to generate fallback prompt: {e}")
        return "Generate a basic PyTest automation script."


def _build_template_prompts(
    template_script: Dict[str, Any],
    target_test_case: Dict[str, Any],
    template_structure_info: Dict[str, Any],
    website_url: str = None,
    custom_instructions: str = None
) -> tuple[str, str]:
    """
    Build separate system and user prompts for template-based script generation.

    This function follows the modern prompt structure pattern used by newer AI modules
    like ai_optimization.py and ai_enhancement.py.

    Args:
        template_script: The optimized script to use as template
        target_test_case: The test case to generate script for
        template_structure_info: Structural analysis of the template
        website_url: Target website URL
        custom_instructions: User's custom instructions (optional)

    Returns:
        tuple: (system_prompt, user_prompt)
    """
    try:
        # Extract template information
        template_content = template_script.get('content', '')
        template_test_case_id = template_script.get('test_case_id', 'Unknown')

        # Extract target test case information
        target_tc_id = target_test_case.get('Test Case ID', 'Unknown')
        target_objective = target_test_case.get('Test Case Objective', 'No objective specified')
        target_steps = target_test_case.get('Steps', [])

        # Build the system prompt (role and high-level instructions)
        system_prompt = """You are a senior QA-automation engineer specializing in PyTest and Selenium WebDriver.
Your expertise is in template-based test script generation, where you adapt proven, optimized test scripts
to work with new test cases while preserving their structure, patterns, and best practices.

Your task is to generate a new PyTest automation script using a provided optimized script as a template.
You must preserve the template's proven patterns while adapting the content for the target test case."""

        # Build the user prompt (specific task and data)
        user_prompt = f"""## Template-Based Script Generation Task

### Template Script Information
- **Original Test Case ID**: {template_test_case_id}
- **Template Type**: Optimized Script (proven structure and patterns)
- **Template Size**: {template_structure_info.get('total_lines', 0)} lines
- **Test Functions**: {len(template_structure_info.get('test_functions', []))}
- **Helper Functions**: {len(template_structure_info.get('helper_functions', []))}
- **Locator Strategies**: {', '.join(template_structure_info.get('locator_strategies', []))}
- **Has Browser Setup**: {template_structure_info.get('has_browser_setup', False)}
- **Has Error Handling**: {template_structure_info.get('has_error_handling', False)}
- **Has Assertions**: {template_structure_info.get('has_assertions', False)}

### Target Test Case
- **Test Case ID**: {target_tc_id}
- **Objective**: {target_objective}
- **Number of Steps**: {len(target_steps)}
{_format_target_steps(target_steps)}

### Template Script (Reference)
```python
{template_content}
```

### Generation Requirements

1. **Structure Preservation**
   - Maintain the same overall script structure as the template
   - Preserve import statements and fixture definitions
   - Keep the same function organization pattern
   - Maintain error handling and browser setup patterns

2. **Content Adaptation**
   - Replace template test case logic with target test case requirements
   - Update function names to reflect the new test case ID
   - Adapt step implementations to match target test case steps
   - Update assertions to match target test case expected results

3. **Best Practices Retention**
   - Preserve all optimization patterns from the template
   - Maintain the template's wait strategies and timeouts
   - Keep the template's error handling approaches
   - Retain the template's assertion patterns

4. **Quality Standards**
   - Generate syntactically correct Python/PyTest code
   - Include all necessary imports
   - Use meaningful function names following pattern: test_{{target_tc_id.lower().replace(' ', '_')}}_functionality
   - Include comprehensive error handling as shown in the template
   - Ensure proper browser cleanup and resource management
   - Add meaningful assertions for each verification step

{f"### Website Context\\nTarget Website: {website_url}" if website_url else ""}

{f"### Custom Instructions\\n{custom_instructions}" if custom_instructions else ""}

### Output Requirements
Return only the complete Python script code without any additional explanation or markdown formatting.
The script should be ready to execute as a PyTest test case.

Generate the new test script now, ensuring it follows the template's proven patterns while implementing the target test case requirements."""

        logger.info(f"Built modern template prompts for {target_tc_id} using template from {template_test_case_id}")
        return system_prompt, user_prompt

    except Exception as e:
        logger.error(f"Failed to build template prompts: {e}")
        # Return fallback prompts
        fallback_system = "You are a senior QA-automation engineer. Generate a PyTest automation script."
        fallback_user = _generate_fallback_prompt(target_test_case)
        return fallback_system, fallback_user


@error_handler
def generate_template_based_script_prompt(
    template_script: Dict[str, Any],
    target_test_case: Dict[str, Any],
    template_structure_info: Dict[str, Any],
    website_url: str = None
) -> str:
    """
    Generate a comprehensive prompt for template-based test script generation.

    Args:
        template_script: The optimized script to use as template
        target_test_case: The test case to generate script for
        template_structure_info: Structural analysis of the template
        website_url: Target website URL

    Returns:
        str: The generated prompt for AI script generation
    """
    try:
        # Extract template information
        template_content = template_script.get('content', '')
        template_test_case_id = template_script.get('test_case_id', 'Unknown')

        # Extract target test case information
        target_tc_id = target_test_case.get('Test Case ID', 'Unknown')
        target_objective = target_test_case.get('Test Case Objective', 'No objective specified')
        target_steps = target_test_case.get('Steps', [])

        # Build the prompt
        prompt = f"""# Template-Based Test Script Generation

## Task
Generate a new PyTest automation script for the target test case using the provided optimized script as a template.
Preserve the template's structure, patterns, and best practices while adapting the content for the new test case.

## Template Script Information
- **Original Test Case ID**: {template_test_case_id}
- **Template Type**: Optimized Script (proven structure and patterns)
- **Template Size**: {template_structure_info.get('total_lines', 0)} lines
- **Test Functions**: {len(template_structure_info.get('test_functions', []))}
- **Helper Functions**: {len(template_structure_info.get('helper_functions', []))}
- **Locator Strategies**: {', '.join(template_structure_info.get('locator_strategies', []))}
- **Has Browser Setup**: {template_structure_info.get('has_browser_setup', False)}
- **Has Error Handling**: {template_structure_info.get('has_error_handling', False)}
- **Has Assertions**: {template_structure_info.get('has_assertions', False)}

## Target Test Case
- **Test Case ID**: {target_tc_id}
- **Objective**: {target_objective}
- **Number of Steps**: {len(target_steps)}
{_format_target_steps(target_steps)}

## Template Script (Reference)
```python
{template_content}
```

## Generation Instructions

### 1. Structure Preservation
- Maintain the same overall script structure as the template
- Preserve import statements and fixture definitions
- Keep the same function organization pattern
- Maintain error handling and browser setup patterns

### 2. Content Adaptation
- Replace template test case logic with target test case requirements
- Update function names to reflect the new test case ID
- Adapt step implementations to match target test case steps
- Update assertions to match target test case expected results

### 3. Locator Strategy Consistency
- Use the same locator strategies as the template where applicable
- Maintain the template's preference for CSS selectors, XPath, etc.
- Follow the template's element identification patterns

### 4. Best Practices Retention
- Preserve all optimization patterns from the template
- Maintain the template's wait strategies and timeouts
- Keep the template's error handling approaches
- Retain the template's assertion patterns

### 5. Code Quality Standards
- Follow the template's coding style and conventions
- Maintain the same level of documentation and comments
- Preserve the template's test organization structure

## Quality Requirements
- The generated script must be syntactically correct Python/PyTest code
- All imports must be properly included
- Function names should follow the pattern: test_{{target_tc_id.lower().replace(' ', '_')}}_functionality
- Include comprehensive error handling as shown in the template
- Ensure proper browser cleanup and resource management
- Add meaningful assertions for each verification step

## Output Format
Return only the complete Python script code without any additional explanation or markdown formatting.
The script should be ready to execute as a PyTest test case.

## Website Context
{f"Target Website: {website_url}" if website_url else "No specific website URL provided"}

Generate the new test script now, ensuring it follows the template's proven patterns while implementing the target test case requirements.
"""

        logger.info(f"Generated template-based script prompt for {target_tc_id} using template from {template_test_case_id}")
        return prompt

    except Exception as e:
        logger.error(f"Failed to generate template-based script prompt: {e}")
        return _generate_fallback_prompt(target_test_case)


@error_handler
def enhance_template_prompt_with_context(
    base_prompt: str,
    additional_context: Dict[str, Any]
) -> str:
    """
    Enhance the template-based prompt with additional context.

    Args:
        base_prompt: The base template prompt
        additional_context: Additional context information

    Returns:
        Enhanced prompt with additional context
    """
    try:
        enhancements = []

        # Add browser context if available
        if additional_context.get('browser_type'):
            enhancements.append(f"Target Browser: {additional_context['browser_type']}")

        # Add environment context if available
        if additional_context.get('test_environment'):
            enhancements.append(f"Test Environment: {additional_context['test_environment']}")

        # Add custom instructions if available
        if additional_context.get('custom_instructions'):
            enhancements.append(f"Custom Instructions: {additional_context['custom_instructions']}")

        if enhancements:
            enhancement_section = "\n## Additional Context\n" + "\n".join(f"- {enhancement}" for enhancement in enhancements)
            # Insert before the final generation instruction
            insertion_point = base_prompt.rfind("Generate the new test script now")
            if insertion_point != -1:
                enhanced_prompt = (
                    base_prompt[:insertion_point] +
                    enhancement_section + "\n\n" +
                    base_prompt[insertion_point:]
                )
                return enhanced_prompt

        return base_prompt

    except Exception as e:
        logger.error(f"Failed to enhance template prompt with context: {e}")
        return base_prompt


# ═══════════════════════════════════════════════════════════════════════════════
# AI EXECUTION FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def _execute_template_ai_call(
    system_prompt: str,
    user_prompt: str,
    model_name: str,
    api_key: str,
    request_id: str,
    template_context: Dict[str, Any]
) -> str:
    """
    Execute the AI call for template-based script generation with comprehensive logging.

    This function follows the modern prompt structure pattern used by newer AI modules.

    Args:
        system_prompt: The system prompt for AI generation
        user_prompt: The user prompt for AI generation
        model_name: The AI model to use
        api_key: API key for Google AI
        request_id: Unique request ID for tracking
        template_context: Context information for logging

    Returns:
        str: The AI response
    """
    debug(f"Executing template AI call - model: {model_name}, request_id: {request_id}")

    # Log the prompt generation
    log_ai_interaction(
        function_name="generate_template_based_script",
        prompt=user_prompt,
        response="<prompt generation>",
        model_name=model_name,
        request_id=request_id,
        context=template_context,
        category="template_script_generation_prompt",
        is_prompt_generation=True
    )

    # Call the LLM to generate the script through centralized function using modern prompt structure
    debug("Calling generate_llm_response for template-based script generation")
    response = generate_llm_response(
        system_prompt=system_prompt,
        user_prompt=user_prompt,
        model_name=model_name,
        api_key=api_key,
        function_name="generate_template_based_script",
        parent_request_id=request_id,
        context=template_context,
        category="template_script_generation"
    )

    debug(f"Template AI call completed - response length: {len(response) if response else 0}")
    return response


def _log_template_generation_process(
    operation: str,
    request_id: str,
    template_context: Dict[str, Any],
    template_script: Dict[str, Any] = None,
    target_test_case: Dict[str, Any] = None,
    generated_script: str = "",
    model_name: str = "N/A",
    total_time_ms: float = 0,
    error: Optional[Exception] = None
) -> None:
    """
    Log the template generation process using comprehensive logging patterns.

    Args:
        operation: The type of operation being logged
        request_id: Unique request ID for tracking
        template_context: Context information for logging
        template_script: The template script being used
        target_test_case: The target test case for generation
        generated_script: The generated script (for length calculation)
        model_name: The AI model used
        total_time_ms: Total processing time in milliseconds
        error: Exception if an error occurred
    """
    debug(f"Logging template generation process - operation: {operation}, request_id: {request_id}")

    if operation == "start":
        template_id = template_script.get('test_case_id', 'unknown') if template_script else 'unknown'
        target_id = target_test_case.get('Test Case ID', 'unknown') if target_test_case else 'unknown'
        template_size = len(template_script.get('content', '')) if template_script else 0

        logger.info(f"Starting template-based script generation [Request ID: {request_id}]")
        logger.info(f"Template: {template_id} ({template_size} chars) -> Target: {target_id}")
        debug(f"Template generation started - template_size: {template_size}, target: {target_id}")

    elif operation == "invalid_template":
        logger.warning(f"Invalid template script provided [Request ID: {request_id}]")
        debug("Invalid template script - returning None")

        # Log the validation failure
        log_ai_interaction(
            function_name="generate_template_based_script",
            prompt="Invalid template script provided",
            response="Template validation failed",
            model_name="N/A",
            request_id=request_id,
            context=template_context,
            category="template_generation_validation_error"
        )

    elif operation == "invalid_test_case":
        logger.warning(f"Invalid target test case provided [Request ID: {request_id}]")
        debug("Invalid target test case - returning None")

        # Log the validation failure
        log_ai_interaction(
            function_name="generate_template_based_script",
            prompt="Invalid target test case provided",
            response="Test case validation failed",
            model_name="N/A",
            request_id=request_id,
            context=template_context,
            category="template_generation_validation_error"
        )

    elif operation == "prompt_generation":
        system_prompt_length = template_context.get('system_prompt_length', 0)
        user_prompt_length = template_context.get('user_prompt_length', 0)
        total_prompt_length = template_context.get('total_prompt_length', 0)
        logger.info(f"Modern template prompts generated successfully (system: {system_prompt_length}, user: {user_prompt_length}, total: {total_prompt_length} chars) [Request ID: {request_id}]")
        debug(f"Modern prompt generation successful - system: {system_prompt_length}, user: {user_prompt_length}, total: {total_prompt_length} chars")

    elif operation == "ai_generation":
        logger.info(f"Calling AI for template-based script generation [Request ID: {request_id}]")
        debug("AI generation phase started")

    elif operation == "code_extraction":
        extracted_length = len(generated_script)
        logger.info(f"Extracting Python code from AI response ({extracted_length} chars) [Request ID: {request_id}]")
        debug(f"Code extraction - extracted length: {extracted_length} chars")

    elif operation == "success":
        template_id = template_script.get('test_case_id', 'unknown') if template_script else 'unknown'
        target_id = target_test_case.get('Test Case ID', 'unknown') if target_test_case else 'unknown'
        template_size = len(template_script.get('content', '')) if template_script else 0
        generated_size = len(generated_script)

        logger.info(f"Template-based script generation completed successfully [Request ID: {request_id}]")
        logger.info(f"Generated {generated_size} chars from {template_size} char template in {total_time_ms:.2f}ms")
        debug(f"Generation successful - template: {template_id}, target: {target_id}, time: {total_time_ms:.2f}ms")

        # Log the successful generation
        log_ai_interaction(
            function_name="generate_template_based_script",
            prompt=f"Template: {template_id} -> Target: {target_id}",
            response=f"Generated script: {generated_size} chars",
            model_name=model_name,
            request_id=request_id,
            context={
                **template_context,
                'template_script_length': template_size,
                'generated_script_length': generated_size,
                'total_processing_time_ms': total_time_ms,
                'template_test_case_id': template_id,
                'target_test_case_id': target_id
            },
            latency_ms=total_time_ms,
            category="template_generation_success"
        )

    elif operation == "error":
        template_id = template_script.get('test_case_id', 'unknown') if template_script else 'unknown'
        target_id = target_test_case.get('Test Case ID', 'unknown') if target_test_case else 'unknown'

        logger.error(f"Template-based script generation failed: {error} [Request ID: {request_id}]")
        debug(f"Generation failed - template: {template_id}, target: {target_id}, error: {type(error).__name__}: {str(error)}")

        # Log the error with detailed information
        error_context = template_context.copy()
        error_context.update({
            'error_type': type(error).__name__,
            'total_processing_time_ms': total_time_ms,
            'template_test_case_id': template_id,
            'target_test_case_id': target_id
        })

        log_ai_interaction(
            function_name="generate_template_based_script",
            prompt=f"Template: {template_id} -> Target: {target_id}",
            response=f"ERROR: {str(error)}",
            model_name=model_name,
            request_id=request_id,
            context=error_context,
            error=error,
            latency_ms=total_time_ms,
            category="template_generation_error"
        )


@error_handler
def generate_template_based_script(
    template_script: Dict[str, Any],
    target_test_case: Dict[str, Any],
    custom_instructions: str = None,
    preserve_structure: bool = True,
    include_error_handling: bool = True,
    website_url: str = None,
    api_key: str = None,
    model_name: str = "gemini-2.0-flash"
) -> Optional[str]:
    """
    Generate a new test script using an optimized script as a template.

    This function orchestrates the template-based script generation process with
    comprehensive logging, error handling, and execution metrics tracking.

    The process includes:
    1. Input validation with detailed logging
    2. Template structure analysis and logging
    3. Prompt generation with metrics tracking
    4. AI execution with comprehensive logging
    5. Response cleaning and validation
    6. Success/error logging with execution metrics

    Args:
        template_script: The optimized script to use as template
        target_test_case: The target test case for generation
        custom_instructions: User's custom instructions (optional)
        preserve_structure: Whether to preserve template structure
        include_error_handling: Whether to include error handling
        website_url: Target website URL (optional)
        api_key: Google AI API key (optional)
        model_name: AI model to use

    Returns:
        str: The generated and cleaned Python script, or None if generation failed
    """
    # Generate a unique request ID for this generation operation
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Create comprehensive context information for logging
    template_context = {
        'template_script_id': template_script.get('id', 'unknown') if template_script else 'unknown',
        'template_test_case_id': template_script.get('test_case_id', 'unknown') if template_script else 'unknown',
        'target_test_case_id': target_test_case.get('Test Case ID', 'unknown') if target_test_case else 'unknown',
        'generation_type': 'template_based',
        'preserve_structure': preserve_structure,
        'include_error_handling': include_error_handling,
        'has_custom_instructions': bool(custom_instructions),
        'custom_instructions_length': len(custom_instructions) if custom_instructions else 0,
        'has_website_url': bool(website_url),
        'model_name': model_name,
        'request_id': request_id
    }

    # Log the start of template generation process
    _log_template_generation_process("start", request_id, template_context, template_script, target_test_case)

    try:
        # ═══════════════════════════════════════════════════════════════════════════════
        # PHASE 1: Input Validation with Detailed Logging
        # ═══════════════════════════════════════════════════════════════════════════════
        logger.info("=== PHASE 1: Input Validation ===")
        debug("Starting input validation phase")

        # Validate template script
        if not template_script or not isinstance(template_script, dict):
            _log_template_generation_process("invalid_template", request_id, template_context)
            return None

        if not template_script.get('content'):
            _log_template_generation_process("invalid_template", request_id, template_context)
            return None

        # Validate target test case
        if not target_test_case or not isinstance(target_test_case, dict):
            _log_template_generation_process("invalid_test_case", request_id, template_context)
            return None

        if not target_test_case.get('Test Case ID'):
            _log_template_generation_process("invalid_test_case", request_id, template_context)
            return None

        # Log validation success
        template_size = len(template_script.get('content', ''))
        target_steps = len(target_test_case.get('Steps', []))
        logger.info(f"Input validation successful - template: {template_size} chars, target: {target_steps} steps")
        debug(f"Validation passed - template_size: {template_size}, target_steps: {target_steps}")

        # ═══════════════════════════════════════════════════════════════════════════════
        # PHASE 2: Template Structure Analysis with Metrics
        # ═══════════════════════════════════════════════════════════════════════════════
        logger.info("=== PHASE 2: Template Structure Analysis ===")
        debug("Starting template structure analysis")

        # Extract template structure information with timing
        structure_start_time = time.time()
        template_structure_info = extract_template_structure_info(
            template_script.get('content', '')
        )
        structure_time_ms = (time.time() - structure_start_time) * 1000

        # Log structure analysis results
        structure_metrics = {
            'total_lines': template_structure_info.get('total_lines', 0),
            'test_functions': len(template_structure_info.get('test_functions', [])),
            'helper_functions': len(template_structure_info.get('helper_functions', [])),
            'locator_strategies': len(template_structure_info.get('locator_strategies', [])),
            'has_browser_setup': template_structure_info.get('has_browser_setup', False),
            'has_error_handling': template_structure_info.get('has_error_handling', False),
            'analysis_time_ms': structure_time_ms
        }

        logger.info(f"Template structure analyzed: {structure_metrics['total_lines']} lines, "
                   f"{structure_metrics['test_functions']} test functions, "
                   f"{structure_metrics['helper_functions']} helpers in {structure_time_ms:.2f}ms")
        debug(f"Structure analysis metrics: {structure_metrics}")

        # Update context with structure information
        template_context.update(structure_metrics)

        # ═══════════════════════════════════════════════════════════════════════════════
        # PHASE 3: Prompt Generation with Metrics Tracking (Modern Structure)
        # ═══════════════════════════════════════════════════════════════════════════════
        logger.info("=== PHASE 3: Prompt Generation ===")
        debug("Starting modern prompt generation phase")

        # Generate the modern system and user prompts with timing
        prompt_start_time = time.time()
        system_prompt, user_prompt = _build_template_prompts(
            template_script=template_script,
            target_test_case=target_test_case,
            template_structure_info=template_structure_info,
            website_url=website_url,
            custom_instructions=custom_instructions
        )
        prompt_time_ms = (time.time() - prompt_start_time) * 1000

        # Log prompt generation metrics
        system_prompt_length = len(system_prompt)
        user_prompt_length = len(user_prompt)
        total_prompt_length = system_prompt_length + user_prompt_length

        template_context['system_prompt_length'] = system_prompt_length
        template_context['user_prompt_length'] = user_prompt_length
        template_context['total_prompt_length'] = total_prompt_length
        template_context['prompt_generation_time_ms'] = prompt_time_ms

        _log_template_generation_process("prompt_generation", request_id, template_context)

        # ═══════════════════════════════════════════════════════════════════════════════
        # PHASE 4: AI Execution with Comprehensive Logging (Modern Structure)
        # ═══════════════════════════════════════════════════════════════════════════════
        logger.info("=== PHASE 4: AI Execution ===")
        _log_template_generation_process("ai_generation", request_id, template_context)

        # Execute AI call with modern prompt structure and comprehensive logging
        ai_start_time = time.time()
        generated_script = _execute_template_ai_call(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model_name=model_name,
            api_key=api_key,
            request_id=request_id,
            template_context=template_context
        )
        ai_time_ms = (time.time() - ai_start_time) * 1000

        # Log AI execution metrics
        template_context['ai_execution_time_ms'] = ai_time_ms
        logger.info(f"AI execution completed in {ai_time_ms:.2f}ms - response: {len(generated_script) if generated_script else 0} chars")
        debug(f"AI execution metrics - time: {ai_time_ms:.2f}ms, response_length: {len(generated_script) if generated_script else 0}")

        # ═══════════════════════════════════════════════════════════════════════════════
        # PHASE 5: Response Processing and Validation
        # ═══════════════════════════════════════════════════════════════════════════════
        logger.info("=== PHASE 5: Response Processing ===")

        if not generated_script or not generated_script.strip():
            logger.error("AI generation failed - empty or None response")
            debug("Empty AI response received")

            # Calculate total time for error logging
            total_time_ms = (time.time() - start_time) * 1000
            _log_template_generation_process(
                operation="error",
                request_id=request_id,
                template_context=template_context,
                template_script=template_script,
                target_test_case=target_test_case,
                model_name=model_name,
                total_time_ms=total_time_ms,
                error=Exception("Empty AI response")
            )
            return None

        # Extract and clean Python code with timing
        extraction_start_time = time.time()
        cleaned_script = extract_python_code_from_response(generated_script)
        extraction_time_ms = (time.time() - extraction_start_time) * 1000

        # Log code extraction metrics
        template_context['code_extraction_time_ms'] = extraction_time_ms
        _log_template_generation_process("code_extraction", request_id, template_context,
                                       generated_script=cleaned_script)

        if not cleaned_script or not cleaned_script.strip():
            logger.error("Code extraction failed - no valid Python code found")
            debug(f"Code extraction failed - raw response preview: {generated_script[:200]}...")

            # Calculate total time for error logging
            total_time_ms = (time.time() - start_time) * 1000
            _log_template_generation_process(
                operation="error",
                request_id=request_id,
                template_context=template_context,
                template_script=template_script,
                target_test_case=target_test_case,
                model_name=model_name,
                total_time_ms=total_time_ms,
                error=Exception("Failed to extract valid Python code")
            )
            return None

        # ═══════════════════════════════════════════════════════════════════════════════
        # PHASE 6: Success Logging with Complete Metrics
        # ═══════════════════════════════════════════════════════════════════════════════
        total_time_ms = (time.time() - start_time) * 1000

        # Log comprehensive success metrics
        _log_template_generation_process(
            operation="success",
            request_id=request_id,
            template_context=template_context,
            template_script=template_script,
            target_test_case=target_test_case,
            generated_script=cleaned_script,
            model_name=model_name,
            total_time_ms=total_time_ms
        )

        return cleaned_script

    except Exception as e:
        # Calculate total time for error logging
        total_time_ms = (time.time() - start_time) * 1000

        # Log comprehensive error information
        _log_template_generation_process(
            operation="error",
            request_id=request_id,
            template_context=template_context,
            template_script=template_script,
            target_test_case=target_test_case,
            model_name=model_name,
            total_time_ms=total_time_ms,
            error=e
        )

        return None


@error_handler
def extract_python_code_from_response(response_text: str) -> str:
    """
    Extract Python code from AI response, removing markdown formatting.

    This function handles various markdown formats that AI models might use
    when returning code, including triple backticks with language identifiers.
    Enhanced with comprehensive logging for debugging and monitoring.

    Args:
        response_text: Raw response from AI that may contain markdown code blocks

    Returns:
        str: Cleaned Python code without markdown formatting
    """
    try:
        debug("Starting Python code extraction from AI response")
        logger.debug(f"Extracting Python code from response ({len(response_text) if response_text else 0} chars)")

        if not response_text or not isinstance(response_text, str):
            logger.warning("Invalid response text provided for code extraction")
            debug("Code extraction failed - invalid input")
            return ""

        original_length = len(response_text)
        cleaned_response = response_text.strip()
        debug(f"Response trimmed from {original_length} to {len(cleaned_response)} chars")

        # Check for markdown code blocks with triple backticks
        if "```" in cleaned_response:
            debug("Found markdown code blocks in response")
            logger.debug("Processing markdown code blocks with triple backticks")

            # Use simple string splitting approach (most reliable)
            parts = cleaned_response.split("```")
            if len(parts) >= 3:
                debug(f"Found {len(parts)} parts after splitting by backticks")
                logger.debug(f"Code block structure detected: {len(parts)} segments")

                # Take the middle part (between first and last ```)
                middle_part = parts[1]
                debug(f"Extracted middle part: {len(middle_part)} characters")

                # Remove language identifier if it's on the first line
                lines = middle_part.split('\n')
                if lines and lines[0].strip() in ['python', 'py', 'Python']:
                    debug("Removing Python language identifier from first line")
                    logger.debug("Removed Python language identifier")
                    cleaned_response = '\n'.join(lines[1:]).strip()
                else:
                    cleaned_response = middle_part.strip()

                debug(f"Code block extracted: {len(cleaned_response)} characters")
                logger.debug(f"Successfully extracted code block: {len(cleaned_response)} chars")
            else:
                debug("Could not find proper code block structure")
                logger.warning("Malformed code block structure - insufficient segments")
                return ""
        else:
            debug("No markdown code blocks found, using response as-is")
            logger.debug("No markdown formatting detected, processing as plain text")

        # Additional cleanup: remove any remaining markdown artifacts
        lines = cleaned_response.split('\n')
        cleaned_lines = []
        removed_lines = 0

        for line in lines:
            # Skip lines that are clearly markdown artifacts
            if line.strip().startswith('```'):
                removed_lines += 1
                continue
            cleaned_lines.append(line)

        if removed_lines > 0:
            debug(f"Removed {removed_lines} markdown artifact lines")
            logger.debug(f"Cleaned up {removed_lines} markdown artifact lines")

        final_code = '\n'.join(cleaned_lines).strip()
        debug(f"Final cleaned code: {len(final_code)} characters")

        # Enhanced validation: check if it looks like Python code
        validation_indicators = {
            'has_imports': 'import ' in final_code,
            'has_functions': 'def ' in final_code,
            'has_pytest': 'pytest' in final_code,
            'has_classes': 'class ' in final_code,
            'has_selenium': any(selenium_term in final_code for selenium_term in ['webdriver', 'selenium', 'driver']),
            'line_count': len(final_code.split('\n')) if final_code else 0
        }

        debug(f"Code validation indicators: {validation_indicators}")
        logger.debug(f"Code validation metrics: {validation_indicators}")

        # Check if it looks like valid Python code
        is_valid_python = (
            final_code and (
                validation_indicators['has_imports'] or
                validation_indicators['has_functions'] or
                validation_indicators['has_pytest'] or
                validation_indicators['has_classes']
            )
        )

        if is_valid_python:
            debug("Code validation passed - looks like valid Python")
            logger.info(f"Code extraction successful: {len(final_code)} chars, {validation_indicators['line_count']} lines")
            return final_code
        else:
            debug("Code validation failed - doesn't look like valid Python")
            logger.warning(f"Code validation failed - extracted content doesn't appear to be Python code")
            logger.debug(f"Code preview (first 200 chars): {final_code[:200]}...")
            return ""

    except Exception as e:
        logger.error(f"Error extracting Python code from response: {e}")
        debug(f"Code extraction exception: {type(e).__name__}: {str(e)}")
        return ""
