```python
import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import time
import random

test_data = {
    "website_url": "https://example.com",
    "username": "valid_user",
    "password": "valid_password"
}


@pytest.mark.order(1)
def test_tc_001_login_success(browser):
    try:
        login_page_url = test_data["website_url"]
        browser.get(login_page_url)
        WebDriverWait(browser, 10).until(EC.url_to_be(login_page_url))
        time.sleep(random.uniform(0.5, 1.5))
    except Exception as e:
        print(f"Exception in step 1 (Navigate): {repr(e)}")
        raise

    try:
        username = test_data["username"]
        locator = (By.ID, "username")  # Assuming username field has ID 'username'
        wait = WebDriverWait(browser, 10)
        field = wait.until(EC.visibility_of_element_located(locator))
        field.click()
        field.clear()
        field.send_keys(username)
        time.sleep(random.uniform(0.5, 1.5))
        WebDriverWait(browser, 5).until(EC.text_to_be_present_in_element_value(locator, username))
        assert field.get_attribute("value") == username, "Username field does not contain the expected text."
    except Exception as e:
        print(f"Exception in step 2 (Enter Username): {repr(e)}")
        raise

    try:
        password = test_data["password"]
        locator = (By.ID, "password")  # Assuming password field has ID 'password'
        wait = WebDriverWait(browser, 10)
        field = wait.until(EC.visibility_of_element_located(locator))
        field.click()
        field.clear()
        field.send_keys(password)
        time.sleep(random.uniform(0.5, 1.5))
        WebDriverWait(browser, 5).until(EC.text_to_be_present_in_element_value(locator, password))
        assert field.get_attribute("value") == password, "Password field does not contain the expected text."
    except Exception as e:
        print(f"Exception in step 3 (Enter Password): {repr(e)}")
        raise

    try:
        locator = (By.ID, "login")  # Assuming login button has ID 'login'
        wait = WebDriverWait(browser, 10)
        button = wait.until(EC.element_to_be_clickable(locator))
        button.click()
        time.sleep(random.uniform(0.5, 1.5))
        # Assuming successful login redirects to a page with ID 'account'
        WebDriverWait(browser, 10).until(EC.presence_of_element_located((By.ID, "account")))
        assert browser.current_url == "https://example.com/account", "Login failed - URL does not match expected after login"
    except Exception as e:
        print(f"Exception in step 4 (Click Login): {repr(e)}")
        raise
```