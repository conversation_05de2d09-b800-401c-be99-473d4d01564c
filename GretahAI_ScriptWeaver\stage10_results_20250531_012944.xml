<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="1" failures="0" skipped="0" tests="1" time="1.666" timestamp="2025-05-31T01:29:50.552400" hostname="sbezugam"><testcase classname="" name="template_generated_TC_001_from_TC_001_20250531_012557" time="0.000"><error message="collection failure">C:\Users\<USER>\anaconda3\Lib\site-packages\_pytest\python.py:487: in importtestmodule
    mod = import_path(
C:\Users\<USER>\anaconda3\Lib\site-packages\_pytest\pathlib.py:591: in import_path
    importlib.import_module(module_name)
C:\Users\<USER>\anaconda3\Lib\importlib\__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
&lt;frozen importlib._bootstrap&gt;:1387: in _gcd_import
    ???
&lt;frozen importlib._bootstrap&gt;:1360: in _find_and_load
    ???
&lt;frozen importlib._bootstrap&gt;:1331: in _find_and_load_unlocked
    ???
&lt;frozen importlib._bootstrap&gt;:935: in _load_unlocked
    ???
C:\Users\<USER>\anaconda3\Lib\site-packages\_pytest\assertion\rewrite.py:169: in exec_module
    source_stat, co = _rewrite_test(fn, self.config)
C:\Users\<USER>\anaconda3\Lib\site-packages\_pytest\assertion\rewrite.py:349: in _rewrite_test
    tree = ast.parse(source, filename=strfn)
C:\Users\<USER>\anaconda3\Lib\ast.py:52: in parse
    return compile(source, filename, mode, flags,
E     File "C:\GenAIJira\JiraOllamaPython\GRETAH-CaseForge\GretahAI_ScriptWeaver\template_generated_TC_001_from_TC_001_20250531_012557.py", line 1
E       ```python
E       ^
E   SyntaxError: invalid syntax</error></testcase></testsuite></testsuites>